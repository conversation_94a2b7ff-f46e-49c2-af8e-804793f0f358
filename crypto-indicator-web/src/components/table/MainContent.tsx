import React from 'react';

import { TableControls } from '@/components/ui/TableControls';
import { CSS_CLASSES } from '@/constants/app';

import { ResponsiveTableContainer } from './ResponsiveTableContainer';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated/index';
import type { FilterConfig, SortColumn, SortDirection } from '@/types/table';

interface MainContentProps {
  processedData: CryptoCurrencyStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  filterConfig: FilterConfig;
  hasActiveFilters: boolean;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  onSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig['usdSignal']) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

export const MainContent: React.FC<MainContentProps> = ({
  processedData,
  btcStatistics,
  totalCount,
  filteredCount,
  loading,
  filterConfig,
  hasActiveFilters,
  onSignalClick,
  onRefresh,
  onSort,
  getSortDirection,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  formatDate,
  findBtcDataForSymbol,
}) => {
  return (
    <div className={CSS_CLASSES.TABLE_CONTAINER}>
      <TableControls
        totalCount={totalCount}
        loading={loading}
        onRefresh={onRefresh}
      />

      <ResponsiveTableContainer
        data={processedData}
        btcStatistics={btcStatistics}
        onSignalClick={(symbol: string, currency: string) => {
          onSignalClick(symbol, currency).catch(error => {
            // eslint-disable-next-line no-console
            console.error('Failed to handle signal click:', error);
          });
        }}
        formatDate={formatDate}
        findBtcDataForSymbol={findBtcDataForSymbol}
        onSort={onSort}
        getSortDirection={getSortDirection}
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
    </div>
  );
};
